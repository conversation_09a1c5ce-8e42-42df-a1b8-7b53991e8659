"""
加密和安全工具模块
提供用户认证和消息安全相关功能
"""

import hashlib
import hmac
import secrets
import time
from typing import Dict, Optional


class AuthManager:
    """用户认证管理器"""

    def __init__(self):
        # 简单的用户数据库（实际项目中应使用真实数据库）
        self.users = {
            "admin": {
                "password_hash": self._hash_password("admin123"),
                "salt": "default_salt",
                "created_at": time.time(),
            }
        }
        self.sessions = {}  # 存储活跃会话

    def _hash_password(self, password: str, salt: str = "default_salt") -> str:
        """使用SHA256哈希密码"""
        return hashlib.sha256((password + salt).encode()).hexdigest()

    def register_user(self, username: str, password: str) -> bool:
        """注册新用户"""
        if username in self.users:
            return False

        salt = secrets.token_hex(16)
        password_hash = self._hash_password(password, salt)

        self.users[username] = {
            "password_hash": password_hash,
            "salt": salt,
            "created_at": time.time(),
        }
        return True

    def authenticate_user(self, username: str, password_hash: str) -> Optional[str]:
        """验证用户凭据，返回会话token"""
        if username not in self.users:
            return None

        user_data = self.users[username]
        expected_hash = user_data["password_hash"]

        # 直接比较哈希值
        if password_hash == expected_hash:
            # 生成会话token
            session_token = secrets.token_hex(32)
            self.sessions[session_token] = {
                "username": username,
                "created_at": time.time(),
                "last_activity": time.time(),
            }
            return session_token

        return None

    def validate_session(self, session_token: str) -> Optional[str]:
        """验证会话token，返回用户名"""
        if session_token not in self.sessions:
            return None

        session = self.sessions[session_token]
        # 检查会话是否过期（24小时）
        if time.time() - session["created_at"] > 86400:
            del self.sessions[session_token]
            return None

        # 更新最后活动时间
        session["last_activity"] = time.time()
        return session["username"]

    def logout_user(self, session_token: str) -> bool:
        """用户登出"""
        if session_token in self.sessions:
            del self.sessions[session_token]
            return True
        return False

    def get_active_users(self) -> list:
        """获取活跃用户列表"""
        current_time = time.time()
        active_users = []

        for token, session in list(self.sessions.items()):
            # 如果5分钟内有活动，认为是活跃用户
            if current_time - session["last_activity"] < 300:
                active_users.append(session["username"])
            else:
                # 清理不活跃的会话
                del self.sessions[token]

        return list(set(active_users))  # 去重


class MessageSecurity:
    """消息安全工具"""

    @staticmethod
    def generate_message_signature(message: str, secret_key: str) -> str:
        """生成消息签名"""
        return hmac.new(
            secret_key.encode(), message.encode(), hashlib.sha256
        ).hexdigest()

    @staticmethod
    def verify_message_signature(message: str, signature: str, secret_key: str) -> bool:
        """验证消息签名"""
        expected_signature = MessageSecurity.generate_message_signature(
            message, secret_key
        )
        return hmac.compare_digest(signature, expected_signature)

    @staticmethod
    def generate_nonce() -> str:
        """生成随机数"""
        return secrets.token_hex(16)


# 全局认证管理器实例
auth_manager = AuthManager()
