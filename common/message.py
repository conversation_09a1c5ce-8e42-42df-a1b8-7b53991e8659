"""
消息格式定义模块
定义协作画板的应用层协议消息格式
"""

import json
import hashlib
import time
from typing import Dict, Any, Optional

# 协议版本
PROTOCOL_VERSION = "1.0"

# 消息类型定义
class MessageType:
    # 认证相关
    AUTH_REQUEST = "auth_request"
    AUTH_RESPONSE = "auth_response"
    
    # 画板操作
    DRAW_LINE = "draw_line"
    DRAW_RECT = "draw_rect"
    DRAW_CIRCLE = "draw_circle"
    DRAW_FREE = "draw_free"
    CLEAR_CANVAS = "clear_canvas"
    
    # 同步相关
    SYNC_REQUEST = "sync_request"
    SYNC_RESPONSE = "sync_response"
    USER_JOIN = "user_join"
    USER_LEAVE = "user_leave"
    
    # 系统消息
    HEARTBEAT = "heartbeat"
    ERROR = "error"
    SUCCESS = "success"

class Message:
    """消息类，封装协议消息的创建和解析"""
    
    def __init__(self, msg_type: str, data: Dict[str, Any], user_id: str = None):
        self.version = PROTOCOL_VERSION
        self.msg_type = msg_type
        self.data = data
        self.user_id = user_id
        self.timestamp = time.time()
        self.msg_id = self._generate_msg_id()
    
    def _generate_msg_id(self) -> str:
        """生成消息ID"""
        content = f"{self.timestamp}{self.msg_type}{self.user_id}"
        return hashlib.md5(content.encode()).hexdigest()[:8]
    
    def to_json(self) -> str:
        """将消息转换为JSON字符串"""
        msg_dict = {
            "version": self.version,
            "msg_type": self.msg_type,
            "msg_id": self.msg_id,
            "user_id": self.user_id,
            "timestamp": self.timestamp,
            "data": self.data
        }
        return json.dumps(msg_dict, ensure_ascii=False)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'Message':
        """从JSON字符串创建消息对象"""
        try:
            msg_dict = json.loads(json_str)
            msg = cls(
                msg_dict["msg_type"],
                msg_dict["data"],
                msg_dict.get("user_id")
            )
            msg.version = msg_dict.get("version", "1.0")
            msg.msg_id = msg_dict.get("msg_id", "")
            msg.timestamp = msg_dict.get("timestamp", time.time())
            return msg
        except (json.JSONDecodeError, KeyError) as e:
            raise ValueError(f"Invalid message format: {e}")
    
    def add_checksum(self) -> str:
        """添加消息校验和"""
        json_str = self.to_json()
        checksum = hashlib.sha256(json_str.encode()).hexdigest()[:16]
        return f"{len(json_str):08d}{checksum}{json_str}"
    
    @classmethod
    def parse_with_checksum(cls, raw_data: str) -> 'Message':
        """解析带校验和的消息"""
        if len(raw_data) < 24:  # 8位长度 + 16位校验和
            raise ValueError("Message too short")
        
        length = int(raw_data[:8])
        checksum = raw_data[8:24]
        json_str = raw_data[24:24+length]
        
        # 验证校验和
        expected_checksum = hashlib.sha256(json_str.encode()).hexdigest()[:16]
        if checksum != expected_checksum:
            raise ValueError("Checksum mismatch")
        
        return cls.from_json(json_str)

# 消息创建辅助函数
def create_auth_request(username: str, password: str) -> Message:
    """创建认证请求消息"""
    password_hash = hashlib.sha256(password.encode()).hexdigest()
    return Message(MessageType.AUTH_REQUEST, {
        "username": username,
        "password_hash": password_hash
    })

def create_draw_message(draw_type: str, coordinates: list, color: str = "#000000", 
                       width: int = 2, user_id: str = None) -> Message:
    """创建绘图消息"""
    return Message(draw_type, {
        "coordinates": coordinates,
        "color": color,
        "width": width
    }, user_id)

def create_error_message(error_code: str, error_msg: str) -> Message:
    """创建错误消息"""
    return Message(MessageType.ERROR, {
        "error_code": error_code,
        "error_message": error_msg
    })
