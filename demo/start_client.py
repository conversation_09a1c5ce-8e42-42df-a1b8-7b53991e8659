#!/usr/bin/env python3
"""
启动协作画板客户端的演示脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from client.gui import CollaborativeDrawingGUI

def main():
    """启动客户端"""
    print("=" * 50)
    print("多人协作画板客户端")
    print("=" * 50)
    print("正在启动客户端GUI...")
    print("默认连接信息:")
    print("  服务器: localhost:8888")
    print("  用户名: admin")
    print("  密码: admin123")
    print("=" * 50)
    
    try:
        app = CollaborativeDrawingGUI()
        app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
