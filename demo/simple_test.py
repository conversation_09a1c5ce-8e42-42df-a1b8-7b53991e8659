#!/usr/bin/env python3
"""
简单的客户端-服务器测试
"""

import sys
import os
import socket
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.message import Message, MessageType, create_auth_request

def test_simple_connection():
    """简单连接测试"""
    print("开始简单连接测试...")
    
    try:
        # 创建socket连接
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client_socket.connect(('localhost', 8888))
        print("连接成功")
        
        # 创建认证消息
        auth_msg = create_auth_request("admin", "admin123")
        print(f"认证消息: {auth_msg.to_json()}")
        
        # 发送消息
        data = auth_msg.add_checksum()
        print(f"发送数据长度: {len(data)}")
        print(f"发送数据前24字符: {data[:24]}")
        
        client_socket.sendall(data.encode('utf-8'))
        print("消息已发送")
        
        # 等待响应
        print("等待服务器响应...")
        time.sleep(2)
        
        # 尝试接收响应
        try:
            response = client_socket.recv(1024)
            print(f"收到响应: {len(response)} 字节")
            if response:
                print(f"响应内容: {response[:100]}...")
        except Exception as e:
            print(f"接收响应时出错: {e}")
        
        client_socket.close()
        print("连接已关闭")
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    test_simple_connection()

if __name__ == "__main__":
    main()
