#!/usr/bin/env python3
"""
逐步测试客户端-服务器通信
"""

import sys
import os
import socket
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.message import Message, MessageType, create_auth_request


def test_step_by_step():
    """逐步测试"""
    print("开始逐步测试...")

    try:
        # 1. 创建socket连接
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client_socket.connect(("localhost", 8888))
        print("✓ 连接成功")

        # 2. 创建认证消息
        auth_msg = create_auth_request("admin", "admin123")
        print(f"✓ 创建认证消息: {auth_msg.msg_type}")

        # 3. 发送消息
        data = auth_msg.add_checksum()
        print(f"✓ 消息长度: {len(data)}")

        client_socket.sendall(data.encode("utf-8"))
        print("✓ 消息已发送")

        # 4. 接收响应 - 先接收头部
        print("开始接收响应...")
        header = b""
        while len(header) < 24:
            chunk = client_socket.recv(24 - len(header))
            if not chunk:
                print("✗ 接收头部失败")
                return
            header += chunk

        print(f"✓ 接收到头部: {len(header)} 字节")
        print(f"  头部内容: {header}")

        # 5. 解析长度
        length = int(header[:8].decode())
        checksum = header[8:24].decode()
        print(f"✓ 解析长度: {length}")
        print(f"✓ 校验和: {checksum}")

        # 6. 接收JSON内容
        json_data = b""
        remaining = length
        while remaining > 0:
            chunk = client_socket.recv(min(remaining, 4096))
            if not chunk:
                print("✗ 接收JSON内容失败")
                return
            json_data += chunk
            remaining -= len(chunk)

        print(f"✓ 接收到JSON: {len(json_data)} 字节")
        print(f"  JSON原始字节: {json_data}")
        try:
            json_str = json_data.decode("utf-8")
            print(f"  JSON内容: {json_str}")
        except UnicodeDecodeError as e:
            print(f"  JSON解码失败: {e}")
            print(f"  尝试忽略错误解码: {json_data.decode('utf-8', errors='ignore')}")
            return

        # 7. 验证校验和
        import hashlib

        expected_checksum = hashlib.sha256(json_data).hexdigest()[:16]
        print(f"✓ 期望校验和: {expected_checksum}")
        print(f"✓ 实际校验和: {checksum}")
        print(f"✓ 校验和匹配: {expected_checksum == checksum}")

        # 8. 解析消息
        full_message = header.decode("utf-8") + json_data.decode("utf-8")
        print(f"✓ 完整消息长度: {len(full_message)}")

        try:
            parsed_msg = Message.parse_with_checksum(full_message)
            print(f"✓ 消息解析成功: {parsed_msg.msg_type}")
            print(f"✓ 消息数据: {parsed_msg.data}")
        except Exception as e:
            print(f"✗ 消息解析失败: {e}")

        client_socket.close()
        print("✓ 连接已关闭")

    except Exception as e:
        print(f"✗ 测试出错: {e}")
        import traceback

        traceback.print_exc()


def main():
    test_step_by_step()


if __name__ == "__main__":
    main()
