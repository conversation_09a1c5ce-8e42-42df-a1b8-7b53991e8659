#!/usr/bin/env python3
"""
调试消息格式的脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.message import Message, MessageType, create_auth_request

def debug_message_format():
    """调试消息格式"""
    print("=" * 50)
    print("调试消息格式")
    print("=" * 50)
    
    # 创建认证消息
    auth_msg = create_auth_request("admin", "admin123")
    
    # 获取JSON字符串
    json_str = auth_msg.to_json()
    print(f"JSON消息: {json_str}")
    print(f"JSON长度: {len(json_str)}")
    
    # 获取带校验和的消息
    checksum_data = auth_msg.add_checksum()
    print(f"带校验和的消息: {checksum_data}")
    print(f"总长度: {len(checksum_data)}")
    
    # 分析消息结构
    length_part = checksum_data[:8]
    checksum_part = checksum_data[8:24]
    json_part = checksum_data[24:]
    
    print(f"长度部分: '{length_part}' -> {int(length_part)}")
    print(f"校验和部分: '{checksum_part}'")
    print(f"JSON部分长度: {len(json_part)}")
    print(f"JSON部分: {json_part}")
    
    # 验证校验和
    import hashlib
    expected_checksum = hashlib.sha256(json_part.encode()).hexdigest()[:16]
    print(f"期望校验和: {expected_checksum}")
    print(f"实际校验和: {checksum_part}")
    print(f"校验和匹配: {expected_checksum == checksum_part}")
    
    # 测试解析
    try:
        parsed_msg = Message.parse_with_checksum(checksum_data)
        print(f"解析成功: {parsed_msg.msg_type}")
    except Exception as e:
        print(f"解析失败: {e}")

def main():
    debug_message_format()

if __name__ == "__main__":
    main()
