#!/usr/bin/env python3
"""
启动协作画板服务器的演示脚本
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from server.server import CollaborativeDrawingServer

def main():
    """启动服务器"""
    print("=" * 50)
    print("多人协作画板服务器")
    print("=" * 50)
    print("正在启动服务器...")
    print("默认监听地址: localhost:8888")
    print("按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    server = CollaborativeDrawingServer()
    
    try:
        server.start_server()
    except KeyboardInterrupt:
        print("\n收到中断信号，正在停止服务器...")
        server.stop_server()
        print("服务器已停止")

if __name__ == "__main__":
    main()
