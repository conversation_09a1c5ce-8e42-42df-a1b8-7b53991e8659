"""
画板状态管理模块
管理共享画板的状态和绘图操作
"""

import json
import time
import threading
from typing import Dict, List, Any, Optional

class DrawingElement:
    """绘图元素基类"""
    
    def __init__(self, element_type: str, coordinates: List[float], 
                 color: str = "#000000", width: int = 2, user_id: str = None):
        self.element_type = element_type
        self.coordinates = coordinates
        self.color = color
        self.width = width
        self.user_id = user_id
        self.timestamp = time.time()
        self.element_id = self._generate_id()
    
    def _generate_id(self) -> str:
        """生成元素ID"""
        import hashlib
        content = f"{self.timestamp}{self.element_type}{self.user_id}{self.coordinates}"
        return hashlib.md5(content.encode()).hexdigest()[:12]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "element_id": self.element_id,
            "element_type": self.element_type,
            "coordinates": self.coordinates,
            "color": self.color,
            "width": self.width,
            "user_id": self.user_id,
            "timestamp": self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DrawingElement':
        """从字典创建绘图元素"""
        element = cls(
            data["element_type"],
            data["coordinates"],
            data.get("color", "#000000"),
            data.get("width", 2),
            data.get("user_id")
        )
        element.element_id = data.get("element_id", element.element_id)
        element.timestamp = data.get("timestamp", element.timestamp)
        return element

class CanvasState:
    """画布状态管理"""
    
    def __init__(self):
        self.elements = []  # 存储所有绘图元素
        self.lock = threading.RLock()  # 线程安全锁
        self.version = 0  # 画布版本号，用于同步
        self.max_elements = 10000  # 最大元素数量限制
    
    def add_element(self, element: DrawingElement) -> bool:
        """添加绘图元素"""
        with self.lock:
            # 检查元素数量限制
            if len(self.elements) >= self.max_elements:
                # 删除最旧的元素
                self.elements.pop(0)
            
            self.elements.append(element)
            self.version += 1
            return True
    
    def clear_canvas(self, user_id: str = None) -> bool:
        """清空画布"""
        with self.lock:
            self.elements.clear()
            self.version += 1
            return True
    
    def get_elements_since_version(self, version: int) -> List[Dict[str, Any]]:
        """获取指定版本之后的所有元素"""
        with self.lock:
            if version >= self.version:
                return []
            
            # 简化实现：返回所有元素
            # 实际项目中应该实现增量同步
            return [element.to_dict() for element in self.elements]
    
    def get_all_elements(self) -> List[Dict[str, Any]]:
        """获取所有绘图元素"""
        with self.lock:
            return [element.to_dict() for element in self.elements]
    
    def get_canvas_info(self) -> Dict[str, Any]:
        """获取画布信息"""
        with self.lock:
            return {
                "version": self.version,
                "element_count": len(self.elements),
                "last_update": max([e.timestamp for e in self.elements]) if self.elements else 0
            }
    
    def remove_elements_by_user(self, user_id: str) -> int:
        """删除指定用户的所有绘图元素"""
        with self.lock:
            original_count = len(self.elements)
            self.elements = [e for e in self.elements if e.user_id != user_id]
            removed_count = original_count - len(self.elements)
            
            if removed_count > 0:
                self.version += 1
            
            return removed_count
    
    def save_to_file(self, filename: str) -> bool:
        """保存画布状态到文件"""
        try:
            with self.lock:
                data = {
                    "version": self.version,
                    "elements": [element.to_dict() for element in self.elements],
                    "saved_at": time.time()
                }
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                return True
        except Exception as e:
            print(f"保存画布状态失败: {e}")
            return False
    
    def load_from_file(self, filename: str) -> bool:
        """从文件加载画布状态"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            with self.lock:
                self.elements.clear()
                for element_data in data.get("elements", []):
                    element = DrawingElement.from_dict(element_data)
                    self.elements.append(element)
                
                self.version = data.get("version", 0)
                
                return True
        except Exception as e:
            print(f"加载画布状态失败: {e}")
            return False

# 全局画布状态实例
canvas_state = CanvasState()
