"""
多人协作画板服务器
基于Socket的TCP服务器，支持多客户端连接和实时同步
"""

import socket
import threading
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.message import Message, MessageType, create_error_message
from common.crypto import auth_manager
from server.drawing_state import canvas_state, DrawingElement


class CollaborativeDrawingServer:
    """协作画板服务器"""

    def __init__(self, host="localhost", port=8888):
        self.host = host
        self.port = port
        self.server_socket = None
        self.clients = {}  # 存储客户端连接信息
        self.running = False
        self.client_lock = threading.RLock()

    def start_server(self):
        """启动服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(10)

            self.running = True
            print(f"协作画板服务器启动成功，监听 {self.host}:{self.port}")

            # 启动心跳检测线程
            heartbeat_thread = threading.Thread(
                target=self._heartbeat_monitor, daemon=True
            )
            heartbeat_thread.start()

            while self.running:
                try:
                    client_socket, client_address = self.server_socket.accept()
                    print(f"新客户端连接: {client_address}")

                    # 为每个客户端创建处理线程
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client_socket, client_address),
                        daemon=True,
                    )
                    client_thread.start()

                except socket.error as e:
                    if self.running:
                        print(f"接受连接时出错: {e}")

        except Exception as e:
            print(f"服务器启动失败: {e}")
        finally:
            self.stop_server()

    def _handle_client(self, client_socket, client_address):
        """处理单个客户端连接"""
        client_id = f"{client_address[0]}:{client_address[1]}"

        try:
            # 初始化客户端信息
            with self.client_lock:
                self.clients[client_id] = {
                    "socket": client_socket,
                    "address": client_address,
                    "user_id": None,
                    "session_token": None,
                    "last_heartbeat": time.time(),
                    "authenticated": False,
                }

            while self.running:
                try:
                    # 接收消息
                    raw_data = self._receive_message(client_socket)
                    if not raw_data:
                        break

                    # 解析消息
                    message = Message.parse_with_checksum(raw_data)

                    # 更新心跳时间
                    with self.client_lock:
                        if client_id in self.clients:
                            self.clients[client_id]["last_heartbeat"] = time.time()

                    # 处理消息
                    self._process_message(client_id, message)

                except ValueError as e:
                    print(f"消息解析错误 {client_id}: {e}")
                    error_msg = create_error_message("PARSE_ERROR", str(e))
                    self._send_message(client_socket, error_msg)

                except socket.error:
                    break

        except Exception as e:
            print(f"处理客户端 {client_id} 时出错: {e}")
        finally:
            self._cleanup_client(client_id)

    def _receive_message(self, client_socket) -> str:
        """接收完整消息"""
        try:
            # 先接收消息头（长度+校验和）
            header = b""
            while len(header) < 24:
                chunk = client_socket.recv(24 - len(header))
                if not chunk:
                    return None
                header += chunk

            # 解析消息长度
            length = int(header[:8].decode())
            if length <= 0 or length > 1000000:  # 防止过大的消息
                print(f"无效的消息长度: {length}")
                return None

            # 接收剩余的JSON消息内容
            remaining_data = b""
            remaining = length  # JSON消息的长度
            while remaining > 0:
                chunk = client_socket.recv(min(remaining, 4096))
                if not chunk:
                    return None
                remaining_data += chunk
                remaining -= len(chunk)

            # 组合完整消息：长度+校验和+JSON
            full_message = header + remaining_data
            return full_message.decode("utf-8")

        except UnicodeDecodeError as e:
            print(f"消息解码错误: {e}")
            return None
        except Exception as e:
            print(f"接收消息时出错: {e}")
            return None

    def _send_message(self, client_socket, message: Message):
        """发送消息到客户端"""
        try:
            data = message.add_checksum().encode("utf-8")
            client_socket.sendall(data)
        except Exception as e:
            print(f"发送消息时出错: {e}")

    def _process_message(self, client_id: str, message: Message):
        """处理客户端消息"""
        client_info = self.clients.get(client_id)
        if not client_info:
            return

        client_socket = client_info["socket"]

        # 处理认证请求
        if message.msg_type == MessageType.AUTH_REQUEST:
            self._handle_auth_request(client_id, message)

        # 需要认证的操作
        elif not client_info["authenticated"]:
            error_msg = create_error_message("AUTH_REQUIRED", "需要先进行身份认证")
            self._send_message(client_socket, error_msg)

        # 处理绘图操作
        elif message.msg_type in [
            MessageType.DRAW_LINE,
            MessageType.DRAW_RECT,
            MessageType.DRAW_CIRCLE,
            MessageType.DRAW_FREE,
        ]:
            self._handle_draw_operation(client_id, message)

        # 处理清空画布
        elif message.msg_type == MessageType.CLEAR_CANVAS:
            self._handle_clear_canvas(client_id, message)

        # 处理同步请求
        elif message.msg_type == MessageType.SYNC_REQUEST:
            self._handle_sync_request(client_id, message)

        # 处理心跳
        elif message.msg_type == MessageType.HEARTBEAT:
            response = Message(MessageType.HEARTBEAT, {"status": "ok"})
            self._send_message(client_socket, response)

    def _handle_auth_request(self, client_id: str, message: Message):
        """处理认证请求"""
        client_info = self.clients.get(client_id)
        if not client_info:
            return

        username = message.data.get("username")
        password_hash = message.data.get("password_hash")

        # 验证用户凭据
        session_token = auth_manager.authenticate_user(username, password_hash)

        if session_token:
            # 认证成功
            with self.client_lock:
                client_info["user_id"] = username
                client_info["session_token"] = session_token
                client_info["authenticated"] = True

            response = Message(
                MessageType.AUTH_RESPONSE,
                {
                    "success": True,
                    "session_token": session_token,
                    "message": "认证成功",
                },
            )

            # 通知其他用户有新用户加入
            self._broadcast_user_join(username)

        else:
            # 认证失败
            response = Message(
                MessageType.AUTH_RESPONSE,
                {"success": False, "message": "用户名或密码错误"},
            )

        self._send_message(client_info["socket"], response)

    def _handle_draw_operation(self, client_id: str, message: Message):
        """处理绘图操作"""
        client_info = self.clients.get(client_id)
        if not client_info:
            return

        # 创建绘图元素
        element = DrawingElement(
            message.msg_type,
            message.data.get("coordinates", []),
            message.data.get("color", "#000000"),
            message.data.get("width", 2),
            client_info["user_id"],
        )

        # 添加到画布状态
        canvas_state.add_element(element)

        # 广播给所有其他客户端
        self._broadcast_message(message, exclude_client=client_id)

    def _handle_clear_canvas(self, client_id: str, message: Message):
        """处理清空画布操作"""
        canvas_state.clear_canvas()
        self._broadcast_message(message, exclude_client=client_id)

    def _handle_sync_request(self, client_id: str, message: Message):
        """处理同步请求"""
        client_info = self.clients.get(client_id)
        if not client_info:
            return

        # 获取所有绘图元素
        elements = canvas_state.get_all_elements()
        canvas_info = canvas_state.get_canvas_info()

        response = Message(
            MessageType.SYNC_RESPONSE,
            {
                "elements": elements,
                "canvas_info": canvas_info,
                "active_users": auth_manager.get_active_users(),
            },
        )

        self._send_message(client_info["socket"], response)

    def _broadcast_message(self, message: Message, exclude_client: str = None):
        """广播消息给所有认证的客户端"""
        with self.client_lock:
            for client_id, client_info in list(self.clients.items()):
                if client_id != exclude_client and client_info["authenticated"]:
                    try:
                        self._send_message(client_info["socket"], message)
                    except:
                        # 发送失败，清理客户端
                        self._cleanup_client(client_id)

    def _broadcast_user_join(self, username: str):
        """广播用户加入消息"""
        message = Message(
            MessageType.USER_JOIN,
            {"username": username, "active_users": auth_manager.get_active_users()},
        )
        self._broadcast_message(message)

    def _cleanup_client(self, client_id: str):
        """清理客户端连接"""
        with self.client_lock:
            if client_id in self.clients:
                client_info = self.clients[client_id]

                # 关闭socket
                try:
                    client_info["socket"].close()
                except:
                    pass

                # 如果用户已认证，通知其他用户离开
                if client_info["authenticated"] and client_info["user_id"]:
                    message = Message(
                        MessageType.USER_LEAVE, {"username": client_info["user_id"]}
                    )
                    self._broadcast_message(message, exclude_client=client_id)

                    # 清理会话
                    if client_info["session_token"]:
                        auth_manager.logout_user(client_info["session_token"])

                del self.clients[client_id]
                print(f"客户端 {client_id} 已断开连接")

    def _heartbeat_monitor(self):
        """心跳监控线程"""
        while self.running:
            current_time = time.time()
            timeout_clients = []

            with self.client_lock:
                for client_id, client_info in self.clients.items():
                    # 检查心跳超时（60秒）
                    if current_time - client_info["last_heartbeat"] > 60:
                        timeout_clients.append(client_id)

            # 清理超时客户端
            for client_id in timeout_clients:
                print(f"客户端 {client_id} 心跳超时，断开连接")
                self._cleanup_client(client_id)

            time.sleep(30)  # 每30秒检查一次

    def stop_server(self):
        """停止服务器"""
        self.running = False

        # 关闭所有客户端连接
        with self.client_lock:
            for client_info in self.clients.values():
                try:
                    client_info["socket"].close()
                except:
                    pass
            self.clients.clear()

        # 关闭服务器socket
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass

        print("服务器已停止")


def main():
    """主函数"""
    server = CollaborativeDrawingServer()

    try:
        server.start_server()
    except KeyboardInterrupt:
        print("\n收到中断信号，正在停止服务器...")
        server.stop_server()


if __name__ == "__main__":
    main()
