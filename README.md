# 多人协作画板项目

基于Socket编程实现的多人实时协作画板系统，支持多用户同时在线绘制矢量图形，所有操作实时同步。

## 项目特性

### 核心功能
- **多人协作**：支持多个用户同时连接，实时同步绘图操作
- **矢量图形**：支持直线、矩形、圆形和自由绘制
- **实时同步**：所有用户的绘制操作实时广播给其他用户
- **用户管理**：基本的用户认证和会话管理
- **画布操作**：支持清空画布、同步画布状态

### 技术特性
- **自定义协议**：设计了完整的应用层协议，支持消息校验和错误处理
- **安全认证**：SHA256哈希密码认证，会话token管理
- **并发处理**：服务器支持多线程处理多客户端连接
- **错误处理**：完善的网络异常处理和重连机制
- **扩展性**：协议版本号支持，模块化设计便于功能扩展

## 项目结构

```
socket/
├── common/                 # 公共模块
│   ├── message.py         # 消息格式定义和协议实现
│   └── crypto.py          # 加密和认证工具
├── server/                # 服务器端
│   ├── server.py          # 主服务器程序
│   └── drawing_state.py   # 画板状态管理
├── client/                # 客户端
│   ├── client.py          # 客户端主程序
│   ├── gui.py            # GUI界面实现
│   └── network.py        # 网络通信模块
├── demo/                  # 演示脚本
├── requirements.txt       # 项目依赖
└── README.md             # 项目文档
```

## 协议设计

### 消息格式
```
[8位长度][16位校验和][JSON消息体]
```

### 消息类型
- **认证类**：`auth_request`, `auth_response`, `register_request`, `register_response`
- **绘图类**：`draw_line`, `draw_rect`, `draw_circle`, `draw_free`
- **同步类**：`sync_request`, `sync_response`, `user_join`, `user_leave`
- **系统类**：`heartbeat`, `error`, `success`, `clear_canvas`

### 安全机制
- SHA256密码哈希
- 消息完整性校验
- 会话token管理
- 心跳检测机制

## 安装和运行

### 环境要求
- Python 3.7+
- tkinter（Python内置GUI库）

### 创建Conda环境（推荐）
```bash
# 创建新的conda环境
conda create -n collaborative_drawing python=3.9
conda activate collaborative_drawing
```

### 运行服务器
```bash
cd socket
python server/server.py
```

服务器将在 `localhost:8888` 启动。

### 运行客户端
```bash
cd socket
python client/client.py
```

## 使用说明

### 服务器端
1. 运行服务器程序
2. 服务器将监听8888端口
3. 支持多个客户端同时连接
4. 自动处理用户认证和消息广播

### 客户端
1. 启动客户端程序
2. 点击"连接"按钮，输入服务器信息和用户凭据
   - 默认用户名：admin
   - 默认密码：admin123
   - 或点击"注册"按钮创建新用户账户
3. 连接成功后可以开始绘图
4. 选择绘图工具：自由绘制、直线、矩形、圆形
5. 设置颜色和线宽
6. 在画布上绘制，操作会实时同步给其他用户

### 功能操作
- **绘图工具**：选择不同的绘图工具进行创作
- **颜色选择**：点击颜色按钮选择绘图颜色
- **线宽调整**：使用数字输入框调整线条粗细
- **清空画布**：清空所有绘图内容（需确认）
- **同步画布**：手动请求同步画布状态
- **用户列表**：查看当前在线用户
- **系统信息**：查看连接状态和操作日志

## 协议详细设计

### 认证流程
1. 客户端发送 `auth_request` 包含用户名和密码哈希
2. 服务器验证凭据，返回 `auth_response` 包含认证结果和会话token
3. 认证成功后，客户端可以进行绘图操作

### 绘图同步流程
1. 用户在客户端进行绘图操作
2. 客户端发送对应的绘图消息到服务器
3. 服务器将消息广播给所有其他已认证的客户端
4. 其他客户端接收消息并在本地画布上重现操作

### 错误处理
- **连接断开**：自动检测并清理客户端连接
- **消息格式错误**：返回错误消息给客户端
- **认证失败**：拒绝未认证用户的操作请求
- **心跳超时**：自动断开无响应的客户端

## 扩展功能

### 已实现的扩展特性
- 消息校验和确保数据完整性
- 会话管理支持用户状态跟踪
- 心跳机制保证连接可靠性
- 版本号支持协议升级

### 可扩展功能
- 画板历史记录和撤销/重做
- 更多绘图工具（文本、图片等）
- 房间/频道系统支持多个画板
- 画板保存和加载功能
- 用户权限管理
- 绘图图层支持

## 测试用例

### 基本功能测试
1. **服务器启动测试**：验证服务器能正常启动并监听端口
2. **客户端连接测试**：验证客户端能成功连接到服务器
3. **用户认证测试**：验证正确和错误的用户凭据处理
4. **绘图同步测试**：验证绘图操作能正确同步到其他客户端

### 并发测试
1. **多客户端连接**：同时连接多个客户端
2. **并发绘图**：多个用户同时进行绘图操作
3. **用户进出**：用户动态加入和离开

### 异常处理测试
1. **网络断开**：模拟网络中断情况
2. **服务器重启**：测试服务器重启后的恢复
3. **消息格式错误**：发送格式错误的消息
4. **认证失败**：使用错误的用户凭据

## 技术亮点

1. **完整的协议设计**：从消息格式到错误处理的完整协议栈
2. **高并发支持**：多线程处理多客户端连接
3. **实时同步**：低延迟的绘图操作同步
4. **安全认证**：基于哈希的密码认证和会话管理
5. **用户友好**：直观的GUI界面和操作体验
6. **代码质量**：模块化设计，良好的代码结构和注释

## 开发者信息

本项目是网络Socket编程课程的实验项目，展示了如何使用Socket API设计和实现一个完整的网络应用。

项目遵循了良好的软件工程实践：
- 模块化设计
- 错误处理
- 代码注释
- 文档完整

适合作为Socket编程和网络应用开发的学习参考。
