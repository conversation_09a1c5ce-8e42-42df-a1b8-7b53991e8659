#!/usr/bin/env python3
"""
协议测试脚本
测试消息格式和协议功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.message import Message, MessageType, create_auth_request, create_draw_message
from common.crypto import auth_manager

def test_message_format():
    """测试消息格式"""
    print("测试消息格式...")
    
    # 创建测试消息
    message = Message(MessageType.DRAW_LINE, {
        "coordinates": [10, 20, 100, 200],
        "color": "#FF0000",
        "width": 3
    }, "test_user")
    
    # 测试JSON序列化
    json_str = message.to_json()
    print(f"JSON消息: {json_str}")
    
    # 测试消息解析
    parsed_message = Message.from_json(json_str)
    print(f"解析后的消息类型: {parsed_message.msg_type}")
    print(f"解析后的数据: {parsed_message.data}")
    
    # 测试带校验和的消息
    checksum_data = message.add_checksum()
    print(f"带校验和的消息长度: {len(checksum_data)}")
    
    # 测试校验和解析
    parsed_with_checksum = Message.parse_with_checksum(checksum_data)
    print(f"校验和验证成功: {parsed_with_checksum.msg_type == message.msg_type}")
    
    print("消息格式测试完成\n")

def test_authentication():
    """测试认证功能"""
    print("测试认证功能...")
    
    # 注册新用户
    import hashlib
    test_password_hash = hashlib.sha256("testpass123".encode()).hexdigest()
    success = auth_manager.register_user("testuser", test_password_hash)
    print(f"用户注册结果: {success}")
    
    # 创建认证请求
    auth_msg = create_auth_request("testuser", "testpass123")
    print(f"认证请求消息: {auth_msg.to_json()}")
    
    # 测试认证
    session_token = auth_manager.authenticate_user("testuser", test_password_hash)
    print(f"认证结果 - Session Token: {session_token}")
    
    # 验证会话
    if session_token:
        username = auth_manager.validate_session(session_token)
        print(f"会话验证结果: {username}")
    
    print("认证功能测试完成\n")

def test_drawing_messages():
    """测试绘图消息"""
    print("测试绘图消息...")
    
    # 测试不同类型的绘图消息
    messages = [
        create_draw_message(MessageType.DRAW_LINE, [0, 0, 100, 100], "#FF0000", 2),
        create_draw_message(MessageType.DRAW_RECT, [10, 10, 50, 50], "#00FF00", 3),
        create_draw_message(MessageType.DRAW_CIRCLE, [25, 25, 75, 75], "#0000FF", 1),
        create_draw_message(MessageType.DRAW_FREE, [[10, 10], [20, 15], [30, 25]], "#FFFF00", 4)
    ]
    
    for i, msg in enumerate(messages):
        print(f"绘图消息 {i+1}: {msg.msg_type}")
        print(f"  坐标: {msg.data['coordinates']}")
        print(f"  颜色: {msg.data['color']}")
        print(f"  线宽: {msg.data['width']}")
    
    print("绘图消息测试完成\n")

def test_error_handling():
    """测试错误处理"""
    print("测试错误处理...")
    
    # 测试无效JSON
    try:
        Message.from_json("invalid json")
    except ValueError as e:
        print(f"无效JSON处理: {e}")
    
    # 测试校验和错误
    try:
        invalid_checksum = "00000100" + "0" * 16 + '{"test": "data"}'
        Message.parse_with_checksum(invalid_checksum)
    except ValueError as e:
        print(f"校验和错误处理: {e}")
    
    # 测试认证失败
    result = auth_manager.authenticate_user("nonexistent", "wrongpass")
    print(f"错误认证结果: {result}")
    
    print("错误处理测试完成\n")

def main():
    """主测试函数"""
    print("=" * 50)
    print("协作画板协议测试")
    print("=" * 50)
    
    test_message_format()
    test_authentication()
    test_drawing_messages()
    test_error_handling()
    
    print("=" * 50)
    print("所有测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
