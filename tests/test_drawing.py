#!/usr/bin/env python3
"""
测试绘图功能的脚本
注意：运行此测试前请确保服务器已启动
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from client.network import NetworkClient
from common.message import MessageType

def test_drawing_operations():
    """测试绘图操作"""
    print("=" * 50)
    print("测试绘图功能")
    print("=" * 50)
    
    # 创建网络客户端
    client = NetworkClient()
    
    # 注册消息处理器
    def on_auth_response(message):
        success = message.data.get("success", False)
        msg = message.data.get("message", "")
        print(f"认证响应: {'成功' if success else '失败'} - {msg}")
    
    def on_draw_message(message):
        print(f"收到绘图消息: {message.msg_type}")
        print(f"  坐标: {message.data.get('coordinates', [])}")
        print(f"  颜色: {message.data.get('color', '#000000')}")
        print(f"  线宽: {message.data.get('width', 2)}")
    
    def on_user_join(message):
        username = message.data.get("username")
        print(f"用户加入: {username}")
    
    def on_error(message):
        error_msg = message.data.get("error_message", "")
        print(f"错误消息: {error_msg}")
    
    client.register_message_handler(MessageType.AUTH_RESPONSE, on_auth_response)
    client.register_message_handler(MessageType.DRAW_LINE, on_draw_message)
    client.register_message_handler(MessageType.DRAW_RECT, on_draw_message)
    client.register_message_handler(MessageType.DRAW_CIRCLE, on_draw_message)
    client.register_message_handler(MessageType.DRAW_FREE, on_draw_message)
    client.register_message_handler(MessageType.USER_JOIN, on_user_join)
    client.register_message_handler(MessageType.ERROR, on_error)
    
    try:
        # 连接到服务器
        print("正在连接到服务器...")
        if client.connect():
            print("连接成功！")
            
            # 尝试认证
            print("正在进行用户认证...")
            if client.authenticate("admin", "admin123"):
                print("认证请求已发送")
                
                # 等待认证响应
                time.sleep(2)
                
                if client.is_authenticated():
                    print("认证成功！开始测试绘图功能...")
                    
                    # 测试直线绘制
                    print("\n测试直线绘制...")
                    client.send_draw_operation(
                        MessageType.DRAW_LINE,
                        [10, 10, 100, 100],
                        "#FF0000",
                        3
                    )
                    time.sleep(1)
                    
                    # 测试矩形绘制
                    print("测试矩形绘制...")
                    client.send_draw_operation(
                        MessageType.DRAW_RECT,
                        [50, 50, 150, 120],
                        "#00FF00",
                        2
                    )
                    time.sleep(1)
                    
                    # 测试圆形绘制
                    print("测试圆形绘制...")
                    client.send_draw_operation(
                        MessageType.DRAW_CIRCLE,
                        [200, 200, 300, 300],
                        "#0000FF",
                        4
                    )
                    time.sleep(1)
                    
                    # 测试自由绘制
                    print("测试自由绘制...")
                    free_points = [(10, 200), (20, 190), (30, 180), (40, 170), (50, 160)]
                    client.send_draw_operation(
                        MessageType.DRAW_FREE,
                        free_points,
                        "#FFFF00",
                        5
                    )
                    time.sleep(1)
                    
                    print("绘图测试完成！")
                    
                    # 等待一段时间观察响应
                    print("等待服务器响应...")
                    time.sleep(3)
                    
                else:
                    print("认证失败")
            else:
                print("发送认证请求失败")
        else:
            print("连接失败")
    
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 断开连接
        print("断开连接...")
        client.disconnect()
        print("测试完成")

def main():
    """主函数"""
    test_drawing_operations()

if __name__ == "__main__":
    main()
