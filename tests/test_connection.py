#!/usr/bin/env python3
"""
测试客户端-服务器连接的脚本
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from client.network import NetworkClient
from common.message import MessageType

def test_connection():
    """测试连接功能"""
    print("=" * 50)
    print("测试客户端-服务器连接")
    print("=" * 50)
    
    # 创建网络客户端
    client = NetworkClient()
    
    # 注册消息处理器
    def on_auth_response(message):
        success = message.data.get("success", False)
        msg = message.data.get("message", "")
        print(f"认证响应: {'成功' if success else '失败'} - {msg}")
    
    def on_sync_response(message):
        elements = message.data.get("elements", [])
        print(f"同步响应: 收到 {len(elements)} 个绘图元素")
    
    def on_user_join(message):
        username = message.data.get("username")
        print(f"用户加入: {username}")
    
    def on_error(message):
        error_msg = message.data.get("error_message", "")
        print(f"错误消息: {error_msg}")
    
    client.register_message_handler(MessageType.AUTH_RESPONSE, on_auth_response)
    client.register_message_handler(MessageType.SYNC_RESPONSE, on_sync_response)
    client.register_message_handler(MessageType.USER_JOIN, on_user_join)
    client.register_message_handler(MessageType.ERROR, on_error)
    
    try:
        # 连接到服务器
        print("正在连接到服务器...")
        if client.connect():
            print("连接成功！")
            
            # 尝试认证
            print("正在进行用户认证...")
            if client.authenticate("admin", "admin123"):
                print("认证请求已发送")
                
                # 等待认证响应
                time.sleep(2)
                
                if client.is_authenticated():
                    print("认证成功！")
                    
                    # 测试同步请求
                    print("请求同步画布...")
                    client.request_sync()
                    
                    # 测试绘图操作
                    print("发送测试绘图操作...")
                    client.send_draw_operation(
                        MessageType.DRAW_LINE,
                        [10, 10, 100, 100],
                        "#FF0000",
                        3
                    )
                    
                    # 等待一段时间观察响应
                    print("等待服务器响应...")
                    time.sleep(3)
                    
                else:
                    print("认证失败")
            else:
                print("发送认证请求失败")
        else:
            print("连接失败")
    
    except Exception as e:
        print(f"测试过程中出错: {e}")
    
    finally:
        # 断开连接
        print("断开连接...")
        client.disconnect()
        print("测试完成")

def main():
    """主函数"""
    test_connection()

if __name__ == "__main__":
    main()
