#!/usr/bin/env python3
"""
测试用户注册功能的脚本
注意：运行此测试前请确保服务器已启动
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from client.network import NetworkClient
from common.message import MessageType

def test_user_registration():
    """测试用户注册功能"""
    print("=" * 50)
    print("测试用户注册功能")
    print("=" * 50)
    
    # 创建网络客户端
    client = NetworkClient()
    
    # 注册消息处理器
    def on_register_response(message):
        success = message.data.get("success", False)
        msg = message.data.get("message", "")
        print(f"注册响应: {'成功' if success else '失败'} - {msg}")
    
    def on_auth_response(message):
        success = message.data.get("success", False)
        msg = message.data.get("message", "")
        print(f"认证响应: {'成功' if success else '失败'} - {msg}")
    
    def on_error(message):
        error_msg = message.data.get("error_message", "")
        print(f"错误消息: {error_msg}")
    
    client.register_message_handler(MessageType.REGISTER_RESPONSE, on_register_response)
    client.register_message_handler(MessageType.AUTH_RESPONSE, on_auth_response)
    client.register_message_handler(MessageType.ERROR, on_error)
    
    try:
        # 连接到服务器
        print("正在连接到服务器...")
        if client.connect():
            print("连接成功！")
            
            # 测试注册新用户
            test_username = "testuser123"
            test_password = "testpass123"
            
            print(f"正在注册新用户: {test_username}")
            if client.register(test_username, test_password):
                print("注册请求已发送")
                
                # 等待注册响应
                time.sleep(3)
                
                # 尝试用新用户登录
                print(f"尝试用新用户登录: {test_username}")
                if client.authenticate(test_username, test_password):
                    print("认证请求已发送")
                    
                    # 等待认证响应
                    time.sleep(3)
                    
                    if client.is_authenticated():
                        print("新用户认证成功！")
                    else:
                        print("新用户认证失败")
                else:
                    print("发送认证请求失败")
            else:
                print("发送注册请求失败")
            
            # 测试重复注册
            print(f"\n测试重复注册用户: {test_username}")
            if client.register(test_username, test_password):
                print("重复注册请求已发送")
                time.sleep(2)
            
            # 测试注册已存在的用户（admin）
            print("\n测试注册已存在的用户: admin")
            if client.register("admin", "admin123"):
                print("注册已存在用户的请求已发送")
                time.sleep(2)
            
            # 测试无效用户名
            print("\n测试无效用户名（太短）")
            if client.register("ab", "password123"):
                print("无效用户名注册请求已发送")
                time.sleep(2)
                
        else:
            print("连接失败")
    
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 断开连接
        print("断开连接...")
        client.disconnect()
        print("测试完成")

def main():
    """主函数"""
    test_user_registration()

if __name__ == "__main__":
    main()
