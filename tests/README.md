# 测试目录

本目录包含多人协作画板项目的各种测试脚本。

## 测试文件说明

### test_protocol.py
- **功能**: 测试协议消息格式和编解码
- **依赖**: 无需服务器运行
- **测试内容**:
  - 消息序列化和反序列化
  - 校验和验证
  - 认证功能
  - 绘图消息格式
  - 错误处理

### test_connection.py
- **功能**: 测试客户端-服务器连接
- **依赖**: 需要服务器运行
- **测试内容**:
  - 网络连接
  - 用户认证
  - 画布同步
  - 基本绘图操作

### test_register.py
- **功能**: 测试用户注册功能
- **依赖**: 需要服务器运行
- **测试内容**:
  - 新用户注册
  - 重复注册检测
  - 用户名验证
  - 注册后登录

### test_drawing.py
- **功能**: 测试绘图功能
- **依赖**: 需要服务器运行
- **测试内容**:
  - 直线绘制
  - 矩形绘制
  - 圆形绘制
  - 自由绘制

## 运行方式

### 方式一：使用主启动脚本
```bash
python run.py
# 然后选择对应的测试选项
```

### 方式二：直接运行测试
```bash
# 协议测试（无需服务器）
python tests/test_protocol.py

# 其他测试（需要先启动服务器）
python demo/start_server.py  # 在另一个终端
python tests/test_connection.py
python tests/test_register.py
python tests/test_drawing.py
```

## 注意事项

1. 除了 `test_protocol.py` 外，其他测试都需要先启动服务器
2. 测试会使用默认的admin账户（admin/admin123）
3. 注册测试会创建新的测试用户
4. 测试完成后会自动断开连接
5. 如果测试失败，请检查服务器是否正常运行
