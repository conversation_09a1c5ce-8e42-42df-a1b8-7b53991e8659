#!/usr/bin/env python3
"""
多人协作画板项目启动脚本
"""

import sys
import os
import subprocess
import time


def print_banner():
    """打印项目横幅"""
    print("=" * 60)
    print("           多人协作画板项目")
    print("        基于Socket编程的实时协作系统")
    print("=" * 60)


def print_menu():
    """打印菜单"""
    print("\n请选择要执行的操作：")
    print("1. 启动服务器")
    print("2. 启动客户端")
    print("3. 运行协议测试")
    print("4. 运行连接测试")
    print("5. 运行注册功能测试")
    print("6. 查看项目文档")
    print("0. 退出")
    print("-" * 40)


def start_server():
    """启动服务器"""
    print("正在启动服务器...")
    try:
        subprocess.run([sys.executable, "demo/start_server.py"], cwd=".")
    except KeyboardInterrupt:
        print("\n服务器已停止")


def start_client():
    """启动客户端"""
    print("正在启动客户端...")
    try:
        subprocess.run([sys.executable, "demo/start_client.py"], cwd=".")
    except KeyboardInterrupt:
        print("\n客户端已关闭")


def run_protocol_test():
    """运行协议测试"""
    print("正在运行协议测试...")
    subprocess.run([sys.executable, "demo/test_protocol.py"], cwd=".")


def run_connection_test():
    """运行连接测试"""
    print("正在运行连接测试...")
    print("注意：请确保服务器已启动")
    input("按回车键继续...")
    subprocess.run([sys.executable, "demo/test_connection.py"], cwd=".")


def run_register_test():
    """运行注册功能测试"""
    print("正在运行注册功能测试...")
    print("注意：请确保服务器已启动")
    input("按回车键继续...")
    subprocess.run([sys.executable, "demo/test_register.py"], cwd=".")


def show_docs():
    """显示文档"""
    print("\n项目文档：")
    print("- README.md: 项目说明文档")
    print("- 实验报告.md: 详细的实验报告")
    print("- requirements.txt: 项目依赖")
    print("\n项目结构：")
    print("- common/: 公共模块（消息格式、加密等）")
    print("- server/: 服务器端代码")
    print("- client/: 客户端代码")
    print("- demo/: 演示和测试脚本")


def main():
    """主函数"""
    print_banner()

    while True:
        print_menu()
        choice = input("请输入选择 (0-6): ").strip()

        if choice == "0":
            print("感谢使用！再见！")
            break
        elif choice == "1":
            start_server()
        elif choice == "2":
            start_client()
        elif choice == "3":
            run_protocol_test()
        elif choice == "4":
            run_connection_test()
        elif choice == "5":
            run_register_test()
        elif choice == "6":
            show_docs()
        else:
            print("无效选择，请重新输入")

        if choice != "0":
            input("\n按回车键返回主菜单...")


if __name__ == "__main__":
    main()
