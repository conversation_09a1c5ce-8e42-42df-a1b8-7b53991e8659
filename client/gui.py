"""
客户端GUI界面模块
使用Tkinter实现协作画板的图形界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, colorchooser
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.message import MessageType
from client.network import NetworkClient


class DrawingCanvas:
    """绘图画布类"""

    def __init__(self, parent, network_client):
        self.parent = parent
        self.network = network_client

        # 画布设置
        self.canvas_width = 800
        self.canvas_height = 600

        # 创建画布
        self.canvas = tk.Canvas(
            parent,
            width=self.canvas_width,
            height=self.canvas_height,
            bg="white",
            relief=tk.SUNKEN,
            borderwidth=2,
        )
        self.canvas.pack(pady=10)

        # 绘图状态
        self.current_tool = "free"  # free, line, rect, circle
        self.current_color = "#000000"
        self.current_width = 2
        self.drawing = False
        self.start_x = 0
        self.start_y = 0
        self.temp_item = None
        self.free_points = []

        # 绑定鼠标事件
        self.canvas.bind("<Button-1>", self.on_mouse_down)
        self.canvas.bind("<B1-Motion>", self.on_mouse_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_mouse_up)

        # 注册网络消息处理器
        self.network.register_message_handler(
            MessageType.DRAW_LINE, self.handle_remote_draw
        )
        self.network.register_message_handler(
            MessageType.DRAW_RECT, self.handle_remote_draw
        )
        self.network.register_message_handler(
            MessageType.DRAW_CIRCLE, self.handle_remote_draw
        )
        self.network.register_message_handler(
            MessageType.DRAW_FREE, self.handle_remote_draw
        )
        self.network.register_message_handler(
            MessageType.CLEAR_CANVAS, self.handle_remote_clear
        )
        self.network.register_message_handler(
            MessageType.SYNC_RESPONSE, self.handle_sync_response
        )

    def on_mouse_down(self, event):
        """鼠标按下事件"""
        if not self.network.is_authenticated():
            # 如果未认证，显示提示信息
            print("请先连接并认证后再进行绘图")
            return

        self.drawing = True
        self.start_x = event.x
        self.start_y = event.y

        if self.current_tool == "free":
            self.free_points = [(event.x, event.y)]

    def on_mouse_drag(self, event):
        """鼠标拖拽事件"""
        if not self.drawing or not self.network.is_authenticated():
            return

        if self.current_tool == "free":
            # 自由绘制
            self.free_points.append((event.x, event.y))
            if len(self.free_points) >= 2:
                # 在本地画布上绘制
                self.canvas.create_line(
                    self.free_points[-2][0],
                    self.free_points[-2][1],
                    self.free_points[-1][0],
                    self.free_points[-1][1],
                    fill=self.current_color,
                    width=self.current_width,
                    capstyle=tk.ROUND,
                    smooth=True,
                )

        elif self.current_tool in ["line", "rect", "circle"]:
            # 预览图形
            if self.temp_item:
                self.canvas.delete(self.temp_item)

            if self.current_tool == "line":
                self.temp_item = self.canvas.create_line(
                    self.start_x,
                    self.start_y,
                    event.x,
                    event.y,
                    fill=self.current_color,
                    width=self.current_width,
                )
            elif self.current_tool == "rect":
                self.temp_item = self.canvas.create_rectangle(
                    self.start_x,
                    self.start_y,
                    event.x,
                    event.y,
                    outline=self.current_color,
                    width=self.current_width,
                )
            elif self.current_tool == "circle":
                self.temp_item = self.canvas.create_oval(
                    self.start_x,
                    self.start_y,
                    event.x,
                    event.y,
                    outline=self.current_color,
                    width=self.current_width,
                )

    def on_mouse_up(self, event):
        """鼠标释放事件"""
        if not self.drawing or not self.network.is_authenticated():
            return

        self.drawing = False

        # 发送绘图操作到服务器
        if self.current_tool == "free" and len(self.free_points) > 1:
            self.network.send_draw_operation(
                MessageType.DRAW_FREE,
                self.free_points,
                self.current_color,
                self.current_width,
            )

        elif self.current_tool == "line":
            coordinates = [self.start_x, self.start_y, event.x, event.y]
            # 在本地画布上绘制最终图形
            self.canvas.create_line(
                coordinates[0],
                coordinates[1],
                coordinates[2],
                coordinates[3],
                fill=self.current_color,
                width=self.current_width,
            )
            # 发送到服务器
            self.network.send_draw_operation(
                MessageType.DRAW_LINE,
                coordinates,
                self.current_color,
                self.current_width,
            )

        elif self.current_tool == "rect":
            coordinates = [self.start_x, self.start_y, event.x, event.y]
            # 在本地画布上绘制最终图形
            self.canvas.create_rectangle(
                coordinates[0],
                coordinates[1],
                coordinates[2],
                coordinates[3],
                outline=self.current_color,
                width=self.current_width,
            )
            # 发送到服务器
            self.network.send_draw_operation(
                MessageType.DRAW_RECT,
                coordinates,
                self.current_color,
                self.current_width,
            )

        elif self.current_tool == "circle":
            coordinates = [self.start_x, self.start_y, event.x, event.y]
            # 在本地画布上绘制最终图形
            self.canvas.create_oval(
                coordinates[0],
                coordinates[1],
                coordinates[2],
                coordinates[3],
                outline=self.current_color,
                width=self.current_width,
            )
            # 发送到服务器
            self.network.send_draw_operation(
                MessageType.DRAW_CIRCLE,
                coordinates,
                self.current_color,
                self.current_width,
            )

        # 清理临时项目
        if self.temp_item:
            self.canvas.delete(self.temp_item)
            self.temp_item = None

        self.free_points = []

    def handle_remote_draw(self, message):
        """处理远程绘图操作"""
        coordinates = message.data.get("coordinates", [])
        color = message.data.get("color", "#000000")
        width = message.data.get("width", 2)

        if message.msg_type == MessageType.DRAW_FREE:
            # 自由绘制
            for i in range(1, len(coordinates)):
                self.canvas.create_line(
                    coordinates[i - 1][0],
                    coordinates[i - 1][1],
                    coordinates[i][0],
                    coordinates[i][1],
                    fill=color,
                    width=width,
                    capstyle=tk.ROUND,
                    smooth=True,
                )

        elif message.msg_type == MessageType.DRAW_LINE:
            self.canvas.create_line(
                coordinates[0],
                coordinates[1],
                coordinates[2],
                coordinates[3],
                fill=color,
                width=width,
            )

        elif message.msg_type == MessageType.DRAW_RECT:
            self.canvas.create_rectangle(
                coordinates[0],
                coordinates[1],
                coordinates[2],
                coordinates[3],
                outline=color,
                width=width,
            )

        elif message.msg_type == MessageType.DRAW_CIRCLE:
            self.canvas.create_oval(
                coordinates[0],
                coordinates[1],
                coordinates[2],
                coordinates[3],
                outline=color,
                width=width,
            )

    def handle_remote_clear(self, message):
        """处理远程清空画布操作"""
        self.canvas.delete("all")

    def handle_sync_response(self, message):
        """处理同步响应"""
        # 清空当前画布
        self.canvas.delete("all")

        # 重绘所有元素
        elements = message.data.get("elements", [])
        for element in elements:
            self._draw_element(element)

    def _draw_element(self, element):
        """绘制单个元素"""
        element_type = element.get("element_type")
        coordinates = element.get("coordinates", [])
        color = element.get("color", "#000000")
        width = element.get("width", 2)

        if element_type == MessageType.DRAW_FREE:
            for i in range(1, len(coordinates)):
                self.canvas.create_line(
                    coordinates[i - 1][0],
                    coordinates[i - 1][1],
                    coordinates[i][0],
                    coordinates[i][1],
                    fill=color,
                    width=width,
                    capstyle=tk.ROUND,
                    smooth=True,
                )

        elif element_type == MessageType.DRAW_LINE:
            self.canvas.create_line(
                coordinates[0],
                coordinates[1],
                coordinates[2],
                coordinates[3],
                fill=color,
                width=width,
            )

        elif element_type == MessageType.DRAW_RECT:
            self.canvas.create_rectangle(
                coordinates[0],
                coordinates[1],
                coordinates[2],
                coordinates[3],
                outline=color,
                width=width,
            )

        elif element_type == MessageType.DRAW_CIRCLE:
            self.canvas.create_oval(
                coordinates[0],
                coordinates[1],
                coordinates[2],
                coordinates[3],
                outline=color,
                width=width,
            )

    def set_tool(self, tool):
        """设置当前工具"""
        self.current_tool = tool

    def set_color(self, color):
        """设置当前颜色"""
        self.current_color = color

    def set_width(self, width):
        """设置当前线宽"""
        self.current_width = width

    def clear_canvas(self):
        """清空画布"""
        if self.network.is_authenticated():
            self.network.clear_canvas()
            self.canvas.delete("all")

    def sync_canvas(self):
        """同步画布"""
        if self.network.is_authenticated():
            self.network.request_sync()


class CollaborativeDrawingGUI:
    """协作画板主界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("多人协作画板")
        self.root.geometry("1000x800")

        # 网络客户端
        self.network = NetworkClient()

        # 用户状态
        self.username = None
        self.connected = False

        # 创建界面
        self.create_widgets()

        # 注册网络回调
        self.network.register_connection_callback(self.on_connection_changed)
        self.network.register_message_handler(MessageType.USER_JOIN, self.on_user_join)
        self.network.register_message_handler(
            MessageType.USER_LEAVE, self.on_user_leave
        )
        self.network.register_message_handler(MessageType.ERROR, self.on_error_message)

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 顶部工具栏
        self.create_toolbar(main_frame)

        # 中间内容区域
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # 左侧画布
        canvas_frame = ttk.Frame(content_frame)
        canvas_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        self.drawing_canvas = DrawingCanvas(canvas_frame, self.network)

        # 右侧信息面板
        self.create_info_panel(content_frame)

        # 底部状态栏
        self.create_status_bar(main_frame)

    def create_toolbar(self, parent):
        """创建工具栏"""
        toolbar = ttk.Frame(parent)
        toolbar.pack(fill=tk.X, pady=(0, 10))

        # 连接控制
        connection_frame = ttk.LabelFrame(toolbar, text="连接")
        connection_frame.pack(side=tk.LEFT, padx=(0, 10))

        self.connect_btn = ttk.Button(
            connection_frame, text="连接", command=self.show_login_dialog
        )
        self.connect_btn.pack(side=tk.LEFT, padx=5, pady=5)

        self.disconnect_btn = ttk.Button(
            connection_frame, text="断开", command=self.disconnect, state=tk.DISABLED
        )
        self.disconnect_btn.pack(side=tk.LEFT, padx=5, pady=5)

        # 绘图工具
        tools_frame = ttk.LabelFrame(toolbar, text="工具")
        tools_frame.pack(side=tk.LEFT, padx=(0, 10))

        self.tool_var = tk.StringVar(value="free")
        tools = [
            ("自由绘制", "free"),
            ("直线", "line"),
            ("矩形", "rect"),
            ("圆形", "circle"),
        ]

        for text, value in tools:
            ttk.Radiobutton(
                tools_frame,
                text=text,
                variable=self.tool_var,
                value=value,
                command=self.on_tool_changed,
            ).pack(side=tk.LEFT, padx=2, pady=5)

        # 颜色和线宽
        style_frame = ttk.LabelFrame(toolbar, text="样式")
        style_frame.pack(side=tk.LEFT, padx=(0, 10))

        self.color_btn = tk.Button(
            style_frame, text="颜色", bg="#000000", command=self.choose_color, width=6
        )
        self.color_btn.pack(side=tk.LEFT, padx=5, pady=5)

        ttk.Label(style_frame, text="线宽:").pack(side=tk.LEFT, padx=(5, 2), pady=5)
        self.width_var = tk.IntVar(value=2)
        width_spin = ttk.Spinbox(
            style_frame,
            from_=1,
            to=20,
            width=5,
            textvariable=self.width_var,
            command=self.on_width_changed,
        )
        width_spin.pack(side=tk.LEFT, padx=(0, 5), pady=5)

        # 操作按钮
        action_frame = ttk.LabelFrame(toolbar, text="操作")
        action_frame.pack(side=tk.LEFT)

        ttk.Button(action_frame, text="清空", command=self.clear_canvas).pack(
            side=tk.LEFT, padx=5, pady=5
        )
        ttk.Button(action_frame, text="同步", command=self.sync_canvas).pack(
            side=tk.LEFT, padx=5, pady=5
        )

    def create_info_panel(self, parent):
        """创建信息面板"""
        info_frame = ttk.Frame(parent, width=200)
        info_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        info_frame.pack_propagate(False)

        # 在线用户
        users_frame = ttk.LabelFrame(info_frame, text="在线用户")
        users_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        self.users_listbox = tk.Listbox(users_frame)
        self.users_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 系统信息
        info_text_frame = ttk.LabelFrame(info_frame, text="系统信息")
        info_text_frame.pack(fill=tk.BOTH, expand=True)

        self.info_text = tk.Text(info_text_frame, height=10, state=tk.DISABLED)
        self.info_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(
            info_text_frame, orient=tk.VERTICAL, command=self.info_text.yview
        )
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.info_text.config(yscrollcommand=scrollbar.set)

    def create_status_bar(self, parent):
        """创建状态栏"""
        self.status_bar = ttk.Label(parent, text="未连接", relief=tk.SUNKEN)
        self.status_bar.pack(fill=tk.X, pady=(10, 0))

    def show_login_dialog(self):
        """显示登录对话框"""
        dialog = LoginDialog(self.root)
        if dialog.result:
            username, password, host, port = dialog.result
            self.connect_to_server(username, password, host, port)

    def connect_to_server(self, username, password, host, port):
        """连接到服务器"""
        self.network.host = host
        self.network.port = int(port)

        if self.network.connect():
            # 尝试认证
            if self.network.authenticate(username, password):
                self.username = username
                self.add_info_message(f"正在认证用户 {username}...")
            else:
                self.add_info_message("认证请求发送失败")
                self.network.disconnect()
        else:
            self.add_info_message("连接服务器失败")

    def disconnect(self):
        """断开连接"""
        self.network.disconnect()

    def on_connection_changed(self, connected):
        """连接状态改变回调"""
        self.connected = connected

        if connected:
            self.connect_btn.config(state=tk.DISABLED)
            self.disconnect_btn.config(state=tk.NORMAL)
            self.status_bar.config(text=f"已连接到服务器")
        else:
            self.connect_btn.config(state=tk.NORMAL)
            self.disconnect_btn.config(state=tk.DISABLED)
            self.status_bar.config(text="未连接")
            self.users_listbox.delete(0, tk.END)

    def on_tool_changed(self):
        """工具改变回调"""
        tool = self.tool_var.get()
        self.drawing_canvas.set_tool(tool)

    def choose_color(self):
        """选择颜色"""
        color = colorchooser.askcolor(title="选择颜色")[1]
        if color:
            self.color_btn.config(bg=color)
            self.drawing_canvas.set_color(color)

    def on_width_changed(self):
        """线宽改变回调"""
        width = self.width_var.get()
        self.drawing_canvas.set_width(width)

    def clear_canvas(self):
        """清空画布"""
        if messagebox.askyesno("确认", "确定要清空画布吗？"):
            self.drawing_canvas.clear_canvas()

    def sync_canvas(self):
        """同步画布"""
        self.drawing_canvas.sync_canvas()
        self.add_info_message("请求同步画布...")

    def on_user_join(self, message):
        """用户加入回调"""
        username = message.data.get("username")
        active_users = message.data.get("active_users", [])

        self.update_user_list(active_users)
        self.add_info_message(f"用户 {username} 加入了画板")

    def on_user_leave(self, message):
        """用户离开回调"""
        username = message.data.get("username")
        self.add_info_message(f"用户 {username} 离开了画板")

    def on_error_message(self, message):
        """错误消息回调"""
        error_msg = message.data.get("error_message", "未知错误")
        self.add_info_message(f"错误: {error_msg}")
        messagebox.showerror("错误", error_msg)

    def update_user_list(self, users):
        """更新用户列表"""
        self.users_listbox.delete(0, tk.END)
        for user in users:
            self.users_listbox.insert(tk.END, user)

    def add_info_message(self, message):
        """添加信息消息"""
        import time

        timestamp = time.strftime("%H:%M:%S")

        self.info_text.config(state=tk.NORMAL)
        self.info_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.info_text.see(tk.END)
        self.info_text.config(state=tk.DISABLED)

    def run(self):
        """运行GUI"""
        self.root.mainloop()


class LoginDialog:
    """登录对话框"""

    def __init__(self, parent):
        self.result = None

        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("连接到服务器")
        self.dialog.geometry("300x200")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.geometry(
            "+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50)
        )

        self.create_widgets()

        # 等待对话框关闭
        self.dialog.wait_window()

    def create_widgets(self):
        """创建对话框组件"""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 服务器设置
        ttk.Label(main_frame, text="服务器地址:").grid(
            row=0, column=0, sticky=tk.W, pady=5
        )
        self.host_entry = ttk.Entry(main_frame, width=20)
        self.host_entry.insert(0, "localhost")
        self.host_entry.grid(row=0, column=1, pady=5)

        ttk.Label(main_frame, text="端口:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.port_entry = ttk.Entry(main_frame, width=20)
        self.port_entry.insert(0, "8888")
        self.port_entry.grid(row=1, column=1, pady=5)

        # 用户信息
        ttk.Label(main_frame, text="用户名:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.username_entry = ttk.Entry(main_frame, width=20)
        self.username_entry.insert(0, "admin")
        self.username_entry.grid(row=2, column=1, pady=5)

        ttk.Label(main_frame, text="密码:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.password_entry = ttk.Entry(main_frame, width=20, show="*")
        self.password_entry.insert(0, "admin123")
        self.password_entry.grid(row=3, column=1, pady=5)

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=20)

        ttk.Button(button_frame, text="连接", command=self.on_connect).pack(
            side=tk.LEFT, padx=5
        )
        ttk.Button(button_frame, text="取消", command=self.on_cancel).pack(
            side=tk.LEFT, padx=5
        )

        # 绑定回车键
        self.dialog.bind("<Return>", lambda e: self.on_connect())

        # 设置焦点
        self.username_entry.focus()

    def on_connect(self):
        """连接按钮回调"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        host = self.host_entry.get().strip()
        port = self.port_entry.get().strip()

        if not username or not password or not host or not port:
            messagebox.showerror("错误", "请填写完整信息")
            return

        try:
            int(port)  # 验证端口号
        except ValueError:
            messagebox.showerror("错误", "端口号必须是数字")
            return

        self.result = (username, password, host, port)
        self.dialog.destroy()

    def on_cancel(self):
        """取消按钮回调"""
        self.dialog.destroy()
