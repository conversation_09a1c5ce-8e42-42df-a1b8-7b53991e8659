"""
客户端网络通信模块
处理与服务器的Socket连接和消息传输
"""

import socket
import threading
import time
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.message import (
    Message,
    MessageType,
    create_auth_request,
    create_register_request,
    create_draw_message,
)


class NetworkClient:
    """网络客户端"""

    def __init__(self, host="localhost", port=8888):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False
        self.authenticated = False
        self.user_id = None
        self.session_token = None

        # 消息处理回调
        self.message_handlers = {}
        self.connection_callbacks = []

        # 线程控制
        self.receive_thread = None
        self.heartbeat_thread = None
        self.running = False

    def connect(self) -> bool:
        """连接到服务器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            self.connected = True
            self.running = True

            # 启动接收线程
            self.receive_thread = threading.Thread(
                target=self._receive_loop, daemon=True
            )
            self.receive_thread.start()

            # 启动心跳线程
            self.heartbeat_thread = threading.Thread(
                target=self._heartbeat_loop, daemon=True
            )
            self.heartbeat_thread.start()

            print(f"已连接到服务器 {self.host}:{self.port}")

            # 通知连接回调
            for callback in self.connection_callbacks:
                callback(True)

            return True

        except Exception as e:
            print(f"连接服务器失败: {e}")
            self.connected = False
            return False

    def disconnect(self):
        """断开连接"""
        self.running = False
        self.connected = False
        self.authenticated = False

        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None

        # 通知连接回调
        for callback in self.connection_callbacks:
            callback(False)

        print("已断开服务器连接")

    def authenticate(self, username: str, password: str) -> bool:
        """用户认证"""
        if not self.connected:
            return False

        auth_message = create_auth_request(username, password)
        return self.send_message(auth_message)

    def register(self, username: str, password: str) -> bool:
        """用户注册"""
        if not self.connected:
            return False

        register_message = create_register_request(username, password)
        return self.send_message(register_message)

    def send_message(self, message: Message) -> bool:
        """发送消息到服务器"""
        if not self.connected or not self.socket:
            return False

        try:
            data = message.add_checksum().encode("utf-8")
            self.socket.sendall(data)
            return True
        except Exception as e:
            print(f"发送消息失败: {e}")
            self.disconnect()
            return False

    def send_draw_operation(
        self, draw_type: str, coordinates: list, color: str = "#000000", width: int = 2
    ) -> bool:
        """发送绘图操作"""
        if not self.authenticated:
            return False

        message = create_draw_message(
            draw_type, coordinates, color, width, self.user_id
        )
        return self.send_message(message)

    def request_sync(self) -> bool:
        """请求同步画布状态"""
        if not self.authenticated:
            return False

        message = Message(MessageType.SYNC_REQUEST, {})
        return self.send_message(message)

    def clear_canvas(self) -> bool:
        """清空画布"""
        if not self.authenticated:
            return False

        message = Message(MessageType.CLEAR_CANVAS, {}, self.user_id)
        return self.send_message(message)

    def _receive_loop(self):
        """消息接收循环"""
        while self.running and self.connected:
            try:
                raw_data = self._receive_message()
                if not raw_data:
                    break

                message = Message.parse_with_checksum(raw_data)
                self._handle_message(message)

            except Exception as e:
                print(f"接收消息时出错: {e}")
                break

        self.disconnect()

    def _receive_message(self) -> str:
        """接收完整消息"""
        try:
            # 先接收消息头（长度+校验和）
            header = b""
            while len(header) < 24:
                chunk = self.socket.recv(24 - len(header))
                if not chunk:
                    return None
                header += chunk

            # 解析消息长度
            length = int(header[:8].decode())
            if length <= 0 or length > 1000000:  # 防止过大的消息
                print(f"无效的消息长度: {length}")
                return None

            # 接收剩余的JSON消息内容
            remaining_data = b""
            remaining = length  # JSON消息的长度
            while remaining > 0:
                chunk = self.socket.recv(min(remaining, 4096))
                if not chunk:
                    return None
                remaining_data += chunk
                remaining -= len(chunk)

            # 组合完整消息：长度+校验和+JSON
            full_message = header + remaining_data
            return full_message.decode("utf-8")

        except UnicodeDecodeError as e:
            print(f"消息解码错误: {e}")
            return None
        except Exception as e:
            print(f"接收消息时出错: {e}")
            return None

    def _handle_message(self, message: Message):
        """处理接收到的消息"""
        # 处理认证响应
        if message.msg_type == MessageType.AUTH_RESPONSE:
            self._handle_auth_response(message)

        # 调用注册的消息处理器
        if message.msg_type in self.message_handlers:
            for handler in self.message_handlers[message.msg_type]:
                try:
                    handler(message)
                except Exception as e:
                    print(f"消息处理器出错: {e}")

    def _handle_auth_response(self, message: Message):
        """处理认证响应"""
        success = message.data.get("success", False)

        if success:
            self.authenticated = True
            self.session_token = message.data.get("session_token")
            print(f"认证成功: {message.data.get('message', '')}")
        else:
            self.authenticated = False
            print(f"认证失败: {message.data.get('message', '')}")

    def _heartbeat_loop(self):
        """心跳循环"""
        while self.running and self.connected:
            if self.authenticated:
                heartbeat_msg = Message(
                    MessageType.HEARTBEAT, {"timestamp": time.time()}
                )
                self.send_message(heartbeat_msg)

            time.sleep(30)  # 每30秒发送一次心跳

    def register_message_handler(self, msg_type: str, handler):
        """注册消息处理器"""
        if msg_type not in self.message_handlers:
            self.message_handlers[msg_type] = []
        self.message_handlers[msg_type].append(handler)

    def register_connection_callback(self, callback):
        """注册连接状态回调"""
        self.connection_callbacks.append(callback)

    def is_connected(self) -> bool:
        """检查连接状态"""
        return self.connected and self.socket is not None

    def is_authenticated(self) -> bool:
        """检查认证状态"""
        return self.authenticated
