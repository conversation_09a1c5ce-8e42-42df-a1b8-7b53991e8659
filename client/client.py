"""
多人协作画板客户端主程序
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from client.gui import CollaborativeDrawingGUI

def main():
    """主函数"""
    try:
        # 创建并运行GUI应用
        app = CollaborativeDrawingGUI()
        app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
